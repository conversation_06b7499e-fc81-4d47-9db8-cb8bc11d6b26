<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <payment>
            <manuallease>
                <active>1</active>
                <model>Webguru\ManualLease\Model\Payment\ManualLease</model>
                <title>Manual Lease</title>
                <allowspecific>0</allowspecific>
                <sort_order>1</sort_order>
                <can_use_checkout>1</can_use_checkout>
                <can_use_internal>1</can_use_internal>
                <can_use_for_multishipping>1</can_use_for_multishipping>
            </manuallease>
        </payment>
    </default>
</config>
