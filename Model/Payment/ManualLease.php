<?php
namespace Webguru\ManualLease\Model\Payment;

use Magento\Payment\Model\Method\AbstractMethod;
use Magento\Quote\Api\Data\CartInterface;

class ManualLease extends AbstractMethod
{
    protected $_code = 'manuallease';
    protected $_isOffline = true;
    protected $_canUseCheckout = true;
    protected $_canUseInternal = true;
    protected $_canUseForMultishipping = true;
    protected $_isInitializeNeeded = false;

    /**
     * Assign data to info model instance
     *
     * @param \Magento\Framework\DataObject|mixed $data
     * @return $this
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function assignData(\Magento\Framework\DataObject $data)
    {
        parent::assignData($data);

        $additionalData = $data->getData('additional_data');
        if (isset($additionalData['lease_id'])) {
            $this->getInfoInstance()->setAdditionalInformation('lease_id', $additionalData['lease_id']);
        }

        return $this;
    }

    /**
     * Validate payment method information object
     *
     * @return $this
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function validate()
    {
        parent::validate();

        $leaseId = $this->getInfoInstance()->getAdditionalInformation('lease_id');
        if (empty($leaseId)) {
            throw new \Magento\Framework\Exception\LocalizedException(__('Lease ID is required.'));
        }

        return $this;
    }

    /**
     * Check whether payment method can be used
     *
     * @param CartInterface|null $quote
     * @return bool
     */
    public function isAvailable(CartInterface $quote = null)
    {
        return parent::isAvailable($quote) && $this->_scopeConfig->isSetFlag(
            'payment/manuallease/active',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
    }
}
