<?php
declare(strict_types=1);

namespace Webguru\ManualLease\Model\Payment;

use Magento\Payment\Model\Method\AbstractMethod;
use Magento\Quote\Api\Data\CartInterface;
use Magento\Framework\DataObject;
use Magento\Framework\Exception\LocalizedException;
use Webguru\ManualLease\Model\LeaseIdValidator;
use Webguru\ManualLease\Api\ConfigInterface;

class ManualLease extends AbstractMethod
{
    protected $_code = 'manuallease';
    protected $_isOffline = true;
    protected $_canUseCheckout = true;
    protected $_canUseInternal = true;
    protected $_canUseForMultishipping = true;
    protected $_isInitializeNeeded = false;

    /**
     * @var LeaseIdValidator
     */
    private $leaseIdValidator;

    /**
     * @var ConfigInterface
     */
    private $config;

    /**
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\Api\ExtensionAttributesFactory $extensionFactory
     * @param \Magento\Framework\Api\AttributeValueFactory $customAttributeFactory
     * @param \Magento\Payment\Helper\Data $paymentData
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Magento\Payment\Model\Method\Logger $logger
     * @param LeaseIdValidator $leaseIdValidator
     * @param ConfigInterface $config
     * @param \Magento\Framework\Model\ResourceModel\AbstractResource|null $resource
     * @param \Magento\Framework\Data\Collection\AbstractDb|null $resourceCollection
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Api\ExtensionAttributesFactory $extensionFactory,
        \Magento\Framework\Api\AttributeValueFactory $customAttributeFactory,
        \Magento\Payment\Helper\Data $paymentData,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Payment\Model\Method\Logger $logger,
        LeaseIdValidator $leaseIdValidator,
        ConfigInterface $config,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        parent::__construct(
            $context,
            $registry,
            $extensionFactory,
            $customAttributeFactory,
            $paymentData,
            $scopeConfig,
            $logger,
            $resource,
            $resourceCollection,
            $data
        );
        $this->leaseIdValidator = $leaseIdValidator;
        $this->config = $config;
    }

    /**
     * Assign data to info model instance
     *
     * @param DataObject $data
     * @return $this
     * @throws LocalizedException
     */
    public function assignData(DataObject $data): self
    {
        parent::assignData($data);

        $additionalData = $data->getData('additional_data');
        if (isset($additionalData['lease_id']) && !empty($additionalData['lease_id'])) {
            $leaseId = $this->leaseIdValidator->sanitize((string)$additionalData['lease_id']);
            if ($this->leaseIdValidator->isValid($leaseId)) {
                $this->getInfoInstance()->setAdditionalInformation('lease_id', $leaseId);
            }
        }

        return $this;
    }

    /**
     * Validate payment method information object
     *
     * @return $this
     * @throws LocalizedException
     */
    public function validate(): self
    {
        parent::validate();

        $leaseId = $this->getInfoInstance()->getAdditionalInformation('lease_id');
        if (empty($leaseId) || !$this->leaseIdValidator->isValid((string)$leaseId)) {
            throw new LocalizedException(__('Please enter a valid Lease ID.'));
        }

        return $this;
    }

    /**
     * Check whether payment method can be used
     *
     * @param CartInterface|null $quote
     * @return bool
     */
    public function isAvailable(?CartInterface $quote = null): bool
    {
        return parent::isAvailable($quote) && $this->config->isActive();
    }
}
