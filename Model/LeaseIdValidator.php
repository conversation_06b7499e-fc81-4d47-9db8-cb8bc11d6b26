<?php
declare(strict_types=1);

namespace Webguru\ManualLease\Model;

class LeaseIdValidator
{
    /**
     * Validate lease ID format
     *
     * @param string $leaseId
     * @return bool
     */
    public function isValid(string $leaseId): bool
    {
        $leaseId = trim($leaseId);
        
        // Check if empty
        if ($leaseId === '') {
            return false;
        }
        
        // Check length (adjust as needed)
        if (strlen($leaseId) < 3 || strlen($leaseId) > 50) {
            return false;
        }
        
        // Check for valid characters (alphanumeric, hyphens, underscores)
        if (!preg_match('/^[a-zA-Z0-9\-_]+$/', $leaseId)) {
            return false;
        }
        
        return true;
    }

    /**
     * Sanitize lease ID
     *
     * @param string $leaseId
     * @return string
     */
    public function sanitize(string $leaseId): string
    {
        // Remove whitespace and convert to uppercase
        $leaseId = strtoupper(trim($leaseId));
        
        // Remove any characters that aren't alphanumeric, hyphens, or underscores
        $leaseId = preg_replace('/[^A-Z0-9\-_]/', '', $leaseId);
        
        return $leaseId;
    }
}
