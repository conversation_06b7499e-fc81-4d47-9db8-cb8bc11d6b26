<!--@subject {{trans "Your %store_name order confirmation" store_name=$store.frontend_name}} @-->
<!--@vars {
"var formattedBillingAddress|raw":"Billing Address",
"var order.increment_id":"Order Id",
"var order.frontend_status":"Order Status",
"var order.created_at|date_format":"Order Date",
"var order.customer_name":"Customer Name",
"var order.is_not_virtual":"Order Type",
"var order.email_customer_note":"Email Order Note",
"var order.frontend_status":"Order Status",
"var formattedShippingAddress|raw":"Shipping Address",
"var payment_html|raw":"Payment Details",
"var formattedShippingAddress|raw":"Shipping Address",
"var order.shipping_description":"Shipping Description",
"var order.shipping_description":"Shipping Method Name",
"var lease_id":"Lease ID",
"var lease_id_label":"Lease ID Label"
} @-->

{{template config_path="design/email/header_template"}}

<table>
    <tr class="email-intro">
        <td>
            <p class="greeting">{{trans "Hello %name," name=$order.customer_name}}</p>
            <p>
                {{trans "Thank you for your order from %store_name." store_name=$store.frontend_name}}
                {{trans "You can check the status of your order by <a href=\"%account_url\">logging into your account</a>." account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}
            </p>
            <p>
                {{trans "If you have questions about your order, you can email us at <a href=\"mailto:%store_email\">%store_email</a> or call us at <a href=\"tel:%store_phone\">%store_phone</a>." store_email=$store_email store_phone=$store_phone |raw}}
            </p>
        </td>
    </tr>
    <tr class="email-summary">
        <td>
            <h1>{{trans "Your Order #%increment_id" increment_id=$order.increment_id}}</h1>
            <p>{{trans "Placed on %created_at" created_at=$order.created_at|date_format}}</p>
            
            {{if lease_id}}
            <p><strong>{{var lease_id_label}}:</strong> {{var lease_id}}</p>
            {{/if}}
        </td>
    </tr>
    <tr class="email-information">
        <td>
            {{layout handle="sales_email_order_items" order=$order area="frontend"}}
        </td>
    </tr>
    <tr class="email-information">
        <td>
            <table class="order-details">
                <tr>
                    <td class="address-details">
                        <h3>{{trans "Billing Info"}}</h3>
                        <p>{{var formattedBillingAddress|raw}}</p>
                    </td>
                    {{depend order.is_not_virtual}}
                    <td class="address-details">
                        <h3>{{trans "Shipping Info"}}</h3>
                        <p>{{var formattedShippingAddress|raw}}</p>
                    </td>
                    {{/depend}}
                </tr>
                <tr>
                    <td class="method-info">
                        <h3>{{trans "Payment Method"}}</h3>
                        {{var payment_html|raw}}
                        {{if lease_id}}
                        <p><strong>{{var lease_id_label}}:</strong> {{var lease_id}}</p>
                        {{/if}}
                    </td>
                    {{depend order.is_not_virtual}}
                    <td class="method-info">
                        <h3>{{trans "Shipping Method"}}</h3>
                        <p>{{var order.shipping_description}}</p>
                    </td>
                    {{/depend}}
                </tr>
            </table>
        </td>
    </tr>
</table>

{{template config_path="design/email/footer_template"}}
