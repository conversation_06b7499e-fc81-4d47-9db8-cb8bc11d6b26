define([
    'Magento_Checkout/js/view/payment/default',
    'ko',
    'jquery',
    'mage/validation'
], function (Component, ko, $) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Webguru_ManualLease/payment/manuallease'
        },

        /**
         * Initialize component
         */
        initialize: function () {
            this._super();
            this.leaseId = ko.observable('');
            return this;
        },

        /**
         * Get payment method data
         * @returns {Object}
         */
        getData: function () {
            return {
                'method': this.item.method,
                'additional_data': {
                    'lease_id': this.leaseId()
                }
            };
        },

        /**
         * Validate payment form
         * @returns {Boolean}
         */
        validate: function () {
            var form = '#' + this.getCode() + '-form';
            return $(form).validation() && $(form).validation('isValid');
        },

        /**
         * Get payment method code
         * @returns {String}
         */
        getCode: function () {
            return 'manuallease';
        },

        /**
         * Check if lease ID is valid
         * @returns {Boolean}
         */
        isLeaseIdValid: function () {
            return this.leaseId() && this.leaseId().trim().length > 0;
        }
    });
});
