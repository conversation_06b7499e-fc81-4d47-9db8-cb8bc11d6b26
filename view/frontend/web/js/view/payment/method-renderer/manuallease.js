define(
    [
        'Magento_Checkout/js/view/payment/default',
        'ko'
    ],
    function (Component, ko) {
        'use strict';
        return Component.extend({
            defaults: {
                template: 'Webguru_ManualLease/payment/manuallease'
            },

            initialize: function () {
                this._super();
                this.leaseId = ko.observable('');
                return this;
            },

            getData: function () {
                return {
                    method: this.item.method,
                    additional_data: {
                        lease_id: this.leaseId()
                    }
                };
            },

            validate: function () {
                var form = 'form[data-role=manuallease-form]';
                return $(form).validation() && $(form).validation('isValid');
            }
        });
    }
);
