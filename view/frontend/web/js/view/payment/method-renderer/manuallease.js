define(
    ['Magento_Checkout/js/view/payment/default'],
    function (Component) {
        'use strict';
        return Component.extend({
            defaults: {
                template: 'Webguru_ManualLease/payment/manuallease',
                leaseId: ''
            },
            getData: function () {
                return {
                    method: this.item.method,
                    additional_data: {
                        lease_id: this.leaseId()
                    }
                };
            }
        });
    }
);
