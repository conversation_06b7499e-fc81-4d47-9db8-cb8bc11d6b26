<div class="payment-method" data-bind="css: {'_active': (getCode() == isChecked())}">
    <div class="payment-method-title field choice">
        <input type="radio"
               name="payment[method]"
               class="radio"
               data-bind="attr: {'id': getCode()}, value: getCode(), checked: isChecked, click: selectPaymentMethod, visible: isRadioButtonVisible()"/>
        <label data-bind="attr: {'for': getCode()}" class="label">
            <span data-bind="text: getTitle()"></span>
        </label>
    </div>

    <div class="payment-method-content">
        <!-- ko foreach: getRegion('messages') -->
        <!-- ko template: getTemplate() --><!-- /ko -->
        <!-- /ko -->

        <div class="payment-method-billing-address">
            <!-- ko foreach: $parent.getRegion(getBillingAddressFormName()) -->
            <!-- ko template: getTemplate() --><!-- /ko -->
            <!-- /ko -->
        </div>

        <form class="form" data-role="manuallease-form" data-bind="attr: {'id': getCode() + '-form'}">
            <fieldset class="fieldset payment items ccard" data-bind="attr: {'id': getCode() + '-form-fields'}">
                <div class="field _required">
                    <label for="manuallease_lease_id" class="label">
                        <span><!-- ko i18n: 'Lease ID'--><!-- /ko --></span>
                    </label>
                    <div class="control">
                        <input type="text"
                               id="manuallease_lease_id"
                               name="payment[lease_id]"
                               class="input-text"
                               data-bind="value: leaseId, attr: {title: $t('Lease ID')}"
                               data-validate="{required:true}"
                               autocomplete="off"/>
                    </div>
                </div>
            </fieldset>
        </form>

        <div class="checkout-agreements-block">
            <!-- ko foreach: $parent.getRegion('before-place-order') -->
                <!-- ko template: getTemplate() --><!-- /ko -->
            <!-- /ko -->
        </div>

        <div class="actions-toolbar">
            <div class="primary">
                <button class="action primary checkout"
                        type="submit"
                        data-bind="
                        click: placeOrder,
                        attr: {title: $t('Place Order')},
                        css: {disabled: !isPlaceOrderActionAllowed()},
                        enable: (getCode() == isChecked())
                        "
                        disabled>
                    <span data-bind="i18n: 'Place Order'"></span>
                </button>
            </div>
        </div>
    </div>
</div>
