# Manual Lease Payment Method for Magento 2.4.6

A professional payment method module that allows customers to enter a lease ID during checkout, similar to the purchase order payment method.

## Features

- ✅ **Secure Lease ID Input** - Customers can enter lease IDs during checkout
- ✅ **Input Validation** - Validates and sanitizes lease ID format
- ✅ **Order Integration** - Lease ID is saved to orders and visible in admin
- ✅ **Email Integration** - Lease ID appears in order confirmation emails
- ✅ **Admin Grid** - Lease ID column in order grid for easy filtering
- ✅ **Multi-store Support** - Configurable per store view
- ✅ **Modern Architecture** - Uses Magento 2.4.6 best practices

## Installation

1. Copy the module to `app/code/Webguru/ManualLease/`
2. Run setup commands:
   ```bash
   php bin/magento module:enable Webguru_ManualLease
   php bin/magento setup:upgrade
   php bin/magento setup:di:compile
   php bin/magento cache:clean
   ```

## Configuration

1. Go to **Admin → Stores → Configuration → Sales → Payment Methods**
2. Find "Manual Lease" section
3. Set **Enabled** to "Yes"
4. Configure title and other settings as needed

## Technical Details

### Architecture
- Uses declarative schema (`db_schema.xml`)
- Implements proper dependency injection
- Follows PSR-4 autoloading standards
- Uses strict typing (PHP 7.4+)

### Security
- Input validation and sanitization
- XSS protection with proper escaping
- No direct SQL queries

### Performance
- Minimal database impact
- Efficient JavaScript component
- Proper caching considerations

## File Structure

```
Webguru/ManualLease/
├── Api/ConfigInterface.php
├── Model/
│   ├── Config.php
│   ├── LeaseIdValidator.php
│   └── Payment/ManualLease.php
├── Observer/
│   ├── SaveLeaseIdToOrder.php
│   └── AddLeaseIdToEmailTemplate.php
├── Block/
│   ├── Adminhtml/Order/View/LeaseInfo.php
│   └── Order/Email/LeaseInfo.php
├── view/frontend/
│   ├── layout/checkout_index_index.xml
│   ├── web/js/view/payment/method-renderer/manuallease.js
│   └── web/template/payment/manuallease.html
└── etc/
    ├── module.xml
    ├── config.xml
    ├── di.xml
    ├── events.xml
    ├── db_schema.xml
    └── adminhtml/system.xml
```

## Requirements

- Magento 2.4.6+
- PHP 7.4+

## License

Proprietary - All rights reserved
