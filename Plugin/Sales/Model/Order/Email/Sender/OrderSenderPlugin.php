<?php
namespace Webguru\ManualLease\Plugin\Sales\Model\Order\Email\Sender;

use Magento\Sales\Model\Order\Email\Sender\OrderSender;
use Magento\Sales\Model\Order;

class OrderSenderPlugin
{
    /**
     * Add lease ID to email template variables
     *
     * @param OrderSender $subject
     * @param bool $result
     * @param Order $order
     * @param bool $forceSyncMode
     * @return bool
     */
    public function afterSend(OrderSender $subject, $result, Order $order, $forceSyncMode = false)
    {
        return $result;
    }

    /**
     * Before send - modify template variables
     *
     * @param OrderSender $subject
     * @param Order $order
     * @param bool $forceSyncMode
     * @return array
     */
    public function beforeSend(OrderSender $subject, Order $order, $forceSyncMode = false)
    {
        if ($order->getPayment()->getMethod() === 'manuallease') {
            $leaseId = $order->getData('manual_lease_id');
            if ($leaseId) {
                // Add lease ID to order data for email template
                $order->setData('lease_id_display', $leaseId);
            }
        }
        
        return [$order, $forceSyncMode];
    }
}
