<?php
namespace Webguru\ManualLease\Block\Order\Email;

use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Sales\Model\Order;

class LeaseInfo extends Template
{
    /**
     * @var Order
     */
    protected $order;

    /**
     * @param Context $context
     * @param array $data
     */
    public function __construct(
        Context $context,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    /**
     * Get order
     *
     * @return Order
     */
    public function getOrder()
    {
        if (!$this->order) {
            $this->order = $this->getData('order');
        }
        return $this->order;
    }

    /**
     * Get lease ID from order
     *
     * @return string|null
     */
    public function getLeaseId()
    {
        $order = $this->getOrder();
        if ($order && $order->getPayment()->getMethod() === 'manuallease') {
            return $order->getData('manual_lease_id');
        }
        return null;
    }

    /**
     * Check if order uses manual lease payment method
     *
     * @return bool
     */
    public function isManualLeasePayment()
    {
        $order = $this->getOrder();
        return $order && $order->getPayment()->getMethod() === 'manuallease';
    }

    /**
     * Get formatted lease ID display
     *
     * @return string
     */
    public function getFormattedLeaseId()
    {
        $leaseId = $this->getLeaseId();
        if ($leaseId) {
            return __('Lease ID: %1', $leaseId);
        }
        return '';
    }
}
