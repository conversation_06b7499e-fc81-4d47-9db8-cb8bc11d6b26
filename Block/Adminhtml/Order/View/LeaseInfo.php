<?php
namespace Webguru\ManualLease\Block\Adminhtml\Order\View;

use Magento\Backend\Block\Template;
use Magento\Backend\Block\Template\Context;
use Magento\Framework\Registry;
use Magento\Sales\Model\Order;

class LeaseInfo extends Template
{
    /**
     * @var Registry
     */
    protected $coreRegistry;

    /**
     * @param Context $context
     * @param Registry $registry
     * @param array $data
     */
    public function __construct(
        Context $context,
        Registry $registry,
        array $data = []
    ) {
        $this->coreRegistry = $registry;
        parent::__construct($context, $data);
    }

    /**
     * Get current order
     *
     * @return Order
     */
    public function getOrder()
    {
        return $this->coreRegistry->registry('current_order');
    }

    /**
     * Get lease ID from order
     *
     * @return string|null
     */
    public function getLeaseId()
    {
        $order = $this->getOrder();
        if ($order && $order->getPayment()->getMethod() === 'manuallease') {
            return $order->getData('manual_lease_id');
        }
        return null;
    }

    /**
     * Check if order uses manual lease payment method
     *
     * @return bool
     */
    public function isManualLeasePayment()
    {
        $order = $this->getOrder();
        return $order && $order->getPayment()->getMethod() === 'manuallease';
    }
}
