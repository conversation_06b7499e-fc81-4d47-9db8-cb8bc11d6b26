<?php
declare(strict_types=1);

namespace Webguru\ManualLease\Api;

interface ConfigInterface
{
    public const PAYMENT_METHOD_CODE = 'manuallease';
    public const XML_PATH_ACTIVE = 'payment/manuallease/active';
    public const XML_PATH_TITLE = 'payment/manuallease/title';
    public const XML_PATH_SORT_ORDER = 'payment/manuallease/sort_order';

    /**
     * Check if payment method is active
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isActive(?int $storeId = null): bool;

    /**
     * Get payment method title
     *
     * @param int|null $storeId
     * @return string
     */
    public function getTitle(?int $storeId = null): string;

    /**
     * Get sort order
     *
     * @param int|null $storeId
     * @return int
     */
    public function getSortOrder(?int $storeId = null): int;
}
