<?php
namespace Webguru\ManualLease\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;

class SaveLeaseIdToOrder implements ObserverInterface
{
    public function execute(Observer $observer)
    {
        $order = $observer->getEvent()->getOrder();
        $quote = $observer->getEvent()->getQuote();

        if ($quote->getPayment()->getMethod() === 'manuallease') {
            $leaseId = $quote->getPayment()->getAdditionalInformation('lease_id');
            if ($leaseId) {
                $order->setData('manual_lease_id', $leaseId);
            }
        }
    }
}
