<?php
namespace Webguru\ManualLease\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;

class AddLeaseIdToEmailTemplate implements ObserverInterface
{
    /**
     * Add lease ID to email template variables
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        $transport = $observer->getEvent()->getTransport();
        $order = $transport['order'] ?? null;

        if ($order && $order->getPayment()->getMethod() === 'manuallease') {
            $leaseId = $order->getData('manual_lease_id');
            if ($leaseId) {
                $transport['lease_id'] = $leaseId;
                $transport['lease_id_label'] = __('Lease ID');
                $transport['lease_id_formatted'] = __('Lease ID: %1', $leaseId);

                // Also add to payment_html if it exists
                if (isset($transport['payment_html'])) {
                    $leaseInfo = '<br/><strong>' . __('Lease ID') . ':</strong> ' . $leaseId;
                    $transport['payment_html'] .= $leaseInfo;
                }
            }
        }
    }
}
