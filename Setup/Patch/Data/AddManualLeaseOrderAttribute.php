<?php
namespace Webguru\ManualLease\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Sales\Setup\SalesSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;

class AddManualLeaseOrderAttribute implements DataPatchInterface
{
    protected $moduleDataSetup;
    protected $salesSetupFactory;

    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        SalesSetupFactory $salesSetupFactory
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->salesSetupFactory = $salesSetupFactory;
    }

    public function apply()
    {
        $setup = $this->moduleDataSetup;
        $setup->getConnection()->startSetup();
        $salesSetup = $this->salesSetupFactory->create(['setup' => $setup]);
        $salesSetup->addAttribute('order', 'manual_lease_id', ['type' => 'text', 'visible' => false, 'nullable' => true]);
        $setup->getConnection()->endSetup();
    }

    public static function getDependencies() { return []; }

    public function getAliases() { return []; }
}
