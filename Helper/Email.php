<?php
namespace Webguru\ManualLease\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Sales\Model\Order;

class Email extends AbstractHelper
{
    /**
     * @param Context $context
     */
    public function __construct(Context $context)
    {
        parent::__construct($context);
    }

    /**
     * Get lease ID from order for email display
     *
     * @param Order $order
     * @return string|null
     */
    public function getLeaseIdForEmail(Order $order)
    {
        if ($order && $order->getPayment()->getMethod() === 'manuallease') {
            return $order->getData('manual_lease_id');
        }
        return null;
    }

    /**
     * Get formatted lease ID display for email
     *
     * @param Order $order
     * @return string
     */
    public function getFormattedLeaseIdForEmail(Order $order)
    {
        $leaseId = $this->getLeaseIdForEmail($order);
        if ($leaseId) {
            return __('Lease ID: %1', $leaseId)->render();
        }
        return '';
    }

    /**
     * Check if order uses manual lease payment method
     *
     * @param Order $order
     * @return bool
     */
    public function isManualLeaseOrder(Order $order)
    {
        return $order && $order->getPayment()->getMethod() === 'manuallease';
    }
}
